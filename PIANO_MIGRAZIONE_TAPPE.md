# Piano di Migrazione: Da Viaggi Singoli a Viaggi Multi-Tappa

## 📋 Analisi dei Requisiti

### Situazione Attuale
- **Viaggio**: contiene una singola traccia GPX
- **Struttura**: titolo, descrizione, durata (giorni/notti), media, GPX singolo
- **Calcoli**: distanza basata su una sola traccia

### Nuova Struttura Richiesta
- **Viaggio**: contenitore di più tappe ordinate
- **Tappa**: unità base con numero ordinale, titolo, descrizione, tipo percorso, galleria immagini, traccia GPX
- **Calcoli automatici**: 
  - Distanza totale = somma distanze singole tappe
  - Numero giorni = numero tappe (1 tappa = 1 giorno)
  - Numero notti = numero tappe - 1

---

## 🎯 Obiettivi del Piano

1. **Preservare compatibilità**: viaggi esistenti devono continuare a funzionare
2. **Migrazione graduale**: possibilità di convertire viaggi esistenti
3. **Nuove funzionalità**: supporto completo per viaggi multi-tappa
4. **Mantenere prestazioni**: ottimizzazione query e calcoli

---

## 📊 Task List Prioritizzati

### 🔴 FASE 1: Fondamenta Database (Priorità ALTA)
**Tempo stimato: 3-4 giorni**

#### Task 1.1: Creazione nuovo modello Stage (Tappa)
- **Descrizione**: Creare il modello Prisma per le tappe
- **File coinvolti**: `prisma/schema.prisma`
- **Dettagli**:
  ```prisma
  model Stage {
    id                String     @id @default(cuid())
    tripId            String
    orderIndex        Int        // Numero ordinale della tappa
    title             String
    description       String?    @db.Text
    distance          String?    // Distanza della tappa (es. "142 km")
    duration          String?    // Durata stimata (es. "4h30' (con soste)")
    elevation         String?    // Dislivello complessivo (es. "2.350 m")
    routeType          String?    @db.Text // Tipo di percorso descrittivo
    mainImage         Json?      // Immagine principale della tappa
    media             Json[]     @default([]) // Galleria immagini aggiuntive
    gpxFile           Json?
    createdAt         DateTime   @default(now())
    updatedAt         DateTime   @updatedAt
    trip              Trip       @relation(fields: [tripId], references: [id], onDelete: Cascade)
    
    @@unique([tripId, orderIndex])
    @@map("stages")
  }
  ```

#### Task 1.2: Aggiornamento modello Trip
- **Descrizione**: Modificare Trip per supportare relazione con Stage
- **File coinvolti**: `prisma/schema.prisma`
- **Dettagli**:
  - Aggiungere relazione `stages Stage[]`
  - Mantenere campi esistenti per compatibilità backward
  - **Logica**: `stages.length > 0` = multi-tappa, `stages.length === 0` = singola tappa legacy

#### Task 1.3: Migrazione database
- **Descrizione**: Creare e applicare migration Prisma
- **Comando**: `npx prisma migrate dev --name add-stages-support`

### 🟡 FASE 2: Tipi TypeScript (Priorità ALTA)
**Tempo stimato: 1-2 giorni**

#### Task 2.1: Definizione tipi Stage
- **File coinvolti**: `src/types/trip.ts`
- **Dettagli**:
  ```typescript
  export interface Stage {
    id: string
    tripId: string
    orderIndex: number
    title: string
    description?: string
    distance?: string        // "142 km"
    duration?: string        // "4h30' (con soste)"
    elevation?: string       // "2.350 m"
    routeType?: string        // Descrizione tipo percorso
    mainImage?: MediaItem    // Immagine principale full-width
    media: MediaItem[]       // Galleria immagini aggiuntive
    gpxFile: GpxFile | null
    createdAt: Date
    updatedAt: Date
  }
  
  export type StageCreationData = Omit<Stage, 'id' | 'tripId' | 'createdAt' | 'updatedAt'>
  export type StageUpdateData = Partial<StageCreationData>
  
  // Helper per validazione dati stage
  export interface StageValidationRules {
    title: { required: true, maxLength: 200 }
    distance: { pattern: /^\d+\s*km$/ }
    duration: { maxLength: 50 }
    elevation: { pattern: /^\d+[\.,]?\d*\s*m$/ }
    routeType: { maxLength: 500 }
  }
  ```

#### Task 2.2: Aggiornamento tipi Trip
- **File coinvolti**: `src/types/trip.ts`
- **Dettagli**:
  - Aggiungere `stages?: Stage[]` a Trip
  - Aggiungere helper function `isMultiStageTrip(trip: Trip): boolean`
  - Mantenere campi legacy per compatibilità

### 🟡 FASE 3: Utility e Servizi Core (Priorità ALTA)
**Tempo stimato: 2-3 giorni**

#### Task 3.1: Aggiornamento trip-utils
- **File coinvolti**: `src/lib/trip-utils.ts`
- **Nuove funzioni**:
  ```typescript
  export const isMultiStageTrip = (trip: Trip): boolean => trip.stages && trip.stages.length > 0
  export const calculateTotalDistance = (trip: Trip): number
  export const calculateTripDuration = (trip: Trip): { days: number, nights: number }
  export const validateStageOrder = (stages: Stage[]): boolean
  export const reorderStages = (stages: Stage[], newOrder: number[]): Stage[]
  ```

#### Task 3.2: Servizi di gestione Stage
- **File da creare**: `src/lib/stage-utils.ts`
- **Funzioni**:
  ```typescript
  export const createStage = (data: StageCreationData): Promise<Stage>
  export const updateStage = (id: string, data: StageUpdateData): Promise<Stage>
  export const deleteStage = (id: string): Promise<void>
  export const getStagesByTripId = (tripId: string): Promise<Stage[]>
  ```

### 🟢 FASE 4: API Endpoints (Priorità MEDIA)
**Tempo stimato: 3-4 giorni**

#### Task 4.1: API per gestione Stage
- **File da creare**: `src/app/api/trips/[id]/stages/route.ts`
- **Endpoints**:
  - `GET /api/trips/[id]/stages` - Lista tappe di un viaggio
  - `POST /api/trips/[id]/stages` - Crea nuova tappa
  
#### Task 4.2: API per singola Stage
- **File da creare**: `src/app/api/trips/[id]/stages/[stageId]/route.ts`
- **Endpoints**:
  - `GET /api/trips/[id]/stages/[stageId]` - Dettaglio tappa
  - `PUT /api/trips/[id]/stages/[stageId]` - Aggiorna tappa
  - `DELETE /api/trips/[id]/stages/[stageId]` - Elimina tappa

#### Task 4.3: Aggiornamento API Trip esistenti
- **File coinvolti**: `src/app/api/trips/[id]/route.ts`, `src/app/api/trips/route.ts`
- **Modifiche**:
  - Includere stages nelle response quando `stages.length > 0`
  - Aggiornare calcoli distanza totale usando `isMultiStageTrip()`
  - Gestire backward compatibility

### 🟢 FASE 5: Componenti UI (Priorità MEDIA)
**Tempo stimato: 5-6 giorni**

#### Task 5.1: Componente StageDisplay
- **File da creare**: `src/components/stages/StageDisplay.tsx`
- **Design Pattern**: Layout verticale basato su mockup TripStage.tsx
- **Features**:
  ```typescript
  interface StageDisplayProps {
    stage: Stage
    index: number
    isEditable?: boolean
    showGPX?: boolean
    onEdit?: () => void
    onDelete?: () => void
  }
  ```
- **Layout Specifico**:
  - Titolo grande: `text-xl font-bold mb-4` con numerazione ("Tappa 1: ...")
  - Metadati strutturati in lista verticale (`space-y-1`):
    - Distanza: `font-medium` + valore
    - Durata stimata: `font-medium` + valore  
    - Dislivello complessivo: `font-medium` + valore
    - Tipo di percorso: `font-medium` + valore
  - Descrizione: `leading-relaxed mb-6`
  - Immagine principale: `w-full h-64 object-cover rounded mb-6`
  - GPXSection integrata (condizionale)
  - Separatore: `border-b border-gray-200 pb-8 mb-8`

#### Task 5.2: Componente GPXSectionStage  
- **File da creare**: `src/components/stages/GPXSectionStage.tsx`
- **Design Pattern**: Basato su GPXSection.tsx aggiornato del mockup
- **Features**:
  ```typescript
  interface GPXSectionStageProps {
    distance: string
    waypoints: number
    filename: string
    onDownload?: () => void
    mapPreview?: string
  }
  ```
- **Layout Specifico**:
  - Header separato: `flex items-center justify-between mb-4`
  - Titolo con icona: `Download` icon + "Traccia GPX"
  - Bottone download: `bg-blue-600 hover:bg-blue-700`
  - Container blu: `bg-blue-50 border-blue-100 rounded-lg p-6`
  - Layout interno: `flex gap-6 h-32`
  - Metriche: `grid grid-cols-2 gap-8` con icone e valori grandi (`text-2xl font-medium text-blue-600`)
  - Mappa: `w-48 flex-shrink-0`
  - Filename: separato con `border-t border-gray-200`

#### Task 5.3: Componente StageTimeline
- **File da creare**: `src/components/stages/StageTimeline.tsx`
- **Features**: Lista verticale di stages con drag & drop
- **Pattern**: Container per multiple StageDisplay components
- **Drag & Drop**: Usando @dnd-kit o react-beautiful-dnd
- **Props**:
  ```typescript
  interface StageTimelineProps {
    stages: Stage[]
    isEditable?: boolean
    onReorder?: (newOrder: Stage[]) => void
    onEditStage?: (stageId: string) => void
    onDeleteStage?: (stageId: string) => void
  }
  ```

#### Task 5.4: Componente StageEditor
- **File da creare**: `src/components/stages/StageEditor.tsx`
- **Features**: Form completo per creazione/modifica stage
- **Campi richiesti**:
  - `title`: Input text (required)
  - `distance`: Input text con validazione pattern
  - `duration`: Input text 
  - `elevation`: Input text con validazione pattern  
  - `routeType`: Textarea per descrizione
  - `description`: Textarea rica per descrizione lunga
  - `mainImage`: Upload singola immagine principale
  - `media`: Upload galleria immagini aggiuntive
  - `gpxFile`: Upload file GPX con preview
- **Validazione**: Usando react-hook-form + zod
- **Layout**: Responsive form con sezioni collassabili

#### Task 5.5: Hook useStageEditor
- **File da creare**: `src/hooks/useStageEditor.ts`
- **Features**: Gestione stato form, validazione, upload files
- **Funzionalità**:
  ```typescript
  export function useStageEditor(tripId: string, stageId?: string) {
    // Form state management
    // File upload handling  
    // Validation with StageValidationRules
    // API calls per save/update
    // Error handling
    return {
      form,
      isLoading,
      uploadProgress,
      saveStage,
      errors
    }
  }
  ```

#### Task 5.6: Aggiornamento TripDetailPage
- **File coinvolti**: `src/app/trips/[slug]/page.tsx`
- **Modifiche**:
  - Condizionale rendering: `isMultiStageTrip(trip)` 
  - Se multi-stage: render `StageTimeline`
  - Se single-stage: render layout classico
  - Integrazione con section "Tappe del viaggio"

#### Task 5.7: Utility Components aggiuntivi
- **StageImageUploader**: `src/components/stages/StageImageUploader.tsx`
  - Upload singola immagine principale con preview
  - Validation dimensioni/formato
  - Integration con storage provider
- **StageGalleryUploader**: `src/components/stages/StageGalleryUploader.tsx`
  - Upload multiple immagini per galleria
  - Drag & drop reordering
  - Preview thumbnails
- **GPXFileUploader**: `src/components/stages/GPXFileUploader.tsx`
  - Upload file GPX con validazione
  - Parsing e preview statistiche (distanza, waypoints)
  - Error handling per file corrotti

#### 📋 Note Implementative Fase 5

##### Design System Integration
- **Colori**: Seguire palette esistente con accenti blu per GPX (`bg-blue-50`, `text-blue-600`)
- **Spaziatura**: Usare pattern consistenti (`mb-4`, `mb-6`, `pb-8`, `mb-8`)
- **Typography**: Titoli `text-xl font-bold`, metadati `font-medium`, corpo `leading-relaxed`
- **Border**: Pattern `border-b border-gray-200` per separatori

##### Responsive Considerations  
- **Mobile**: Layout verticale funziona bene, considerare stack per GPX section
- **Tablet**: Mantenere layout, possibile ottimizzazione per GPX grid
- **Desktop**: Layout ottimale come da mockup

##### Performance Optimizations
- **Lazy loading**: Immagini stages con `loading="lazy"`
- **Image optimization**: Next.js Image component per mainImage
- **GPX parsing**: Defer parsing fino al click download/preview
- **Virtual scrolling**: Per liste molto lunghe di stages

##### Accessibility
- **Keyboard navigation**: Drag & drop accessibile
- **Screen readers**: Proper ARIA labels per metadati
- **Focus management**: Nel StageEditor form
- **Color contrast**: Verificare contrasto testi su sfondi colorati

##### Dependencies Required
```json
{
  "dependencies": {
    "@dnd-kit/core": "^6.0.0",
    "@dnd-kit/sortable": "^8.0.0", 
    "@dnd-kit/utilities": "^3.0.0",
    "react-hook-form": "^7.0.0",
    "@hookform/resolvers": "^3.0.0",
    "zod": "^3.0.0"
  }
}
```

##### File Structure Preview
```
src/components/stages/
├── StageDisplay.tsx           # Main stage display component
├── GPXSectionStage.tsx        # GPX section with new design
├── StageTimeline.tsx          # List with drag & drop
├── StageEditor.tsx            # Form for create/edit
├── StageImageUploader.tsx     # Main image upload
├── StageGalleryUploader.tsx   # Gallery images upload
└── GPXFileUploader.tsx        # GPX file upload

src/hooks/
├── useStages.ts              # CRUD operations
└── useStageEditor.ts         # Form management
```

### 🔵 FASE 6: Hook e State Management (Priorità MEDIA)
**Tempo stimato: 2-3 giorni**

#### Task 6.1: Hook useStages
- **File da creare**: `src/hooks/useStages.ts`
- **Features**: gestione CRUD tappe, riordinamento, validazione
- **Funzionalità**:
  ```typescript
  export function useStages(tripId: string) {
    // Fetching stages data
    // CRUD operations (create, update, delete)
    // Reordering logic with optimistic updates
    // Local state management
    // Error handling
    return {
      stages,
      isLoading,
      createStage,
      updateStage,
      deleteStage,
      reorderStages,
      refreshStages
    }
  }
  ```

#### Task 6.2: Hook useStageEditor (se non già implementato in Fase 5)
- **File**: `src/hooks/useStageEditor.ts`
- **Integration**: Con useStages per operazioni CRUD
- **Form management**: react-hook-form + zod validation

#### Task 6.3: Aggiornamento useTripForm
- **File coinvolti**: `src/hooks/useTripForm.ts`
- **Modifiche**: 
  - Gestione array tappe nel form state
  - Validation rules aggiornate per includere stages

### 🔵 FASE 7: Migrazione Dati (Priorità BASSA)
**Tempo stimato: 1-2 giorni**

#### Task 7.1: Script di migrazione viaggi esistenti
- **File da creare**: `src/scripts/migrate-existing-trips.ts`
- **Logica**:
  - Identificare viaggi con singola traccia GPX
  - Creare una tappa equivalente per ogni viaggio
  - Mantenere tutti i dati esistenti

#### Task 7.2: Comando di rollback
- **File da creare**: `src/scripts/rollback-migration.ts`
- **Sicurezza**: possibilità di tornare indietro se necessario

### 🟣 FASE 8: Testing (Priorità MEDIA)
**Tempo stimato: 3-4 giorni**

#### Task 8.1: Test unitari Stage utilities
- **File da creare**: `src/tests/unit/lib/stageUtils.test.ts`

#### Task 8.2: Test integrazione API Stages
- **File da creare**: `src/tests/integration/stages-api.test.ts`

#### Task 8.3: Test componenti UI
- **File da creare**: `src/tests/unit/components/stages/StageCard.test.tsx`

#### Task 8.4: Test migrazione dati
- **File da creare**: `src/tests/integration/data-migration.test.ts`

---

## 🚀 Timeline Esecuzione

### Sprint 1 (Settimana 1)
- **FASE 1**: Database foundations
- **FASE 2**: TypeScript types
- **Obiettivo**: Struttura dati pronta

### Sprint 2 (Settimana 2)
- **FASE 3**: Core utilities e servizi
- **FASE 4**: API endpoints
- **Obiettivo**: Backend funzionante

### Sprint 3 (Settimana 3)
- **FASE 5**: Componenti UI
- **FASE 6**: Hook e state management
- **Obiettivo**: Frontend completo

### Sprint 4 (Settimana 4)
- **FASE 7**: Migrazione dati esistenti
- **FASE 8**: Testing completo
- **Obiettivo**: Sistema ready per produzione

---

## ⚠️ Considerazioni Tecniche

### Compatibilità Backward
- I viaggi esistenti rimangono funzionanti (array `stages` vuoto)
- Logica: `stages.length > 0` = multi-tappa, `stages.length === 0` = legacy
- API response adattive basate sulla presenza di stages

### Performance
- Lazy loading delle tappe quando necessario
- Batch operations per operazioni multiple
- Caching dei calcoli di distanza totale

### Sicurezza
- Validazione ordinamento tappe
- Controllo permessi per modifica ordine
- Sanitizzazione input testo libero

### UX/UI
- Indicatori visivi per viaggi multi-tappa
- Transizioni smooth tra visualizzazioni
- Feedback immediato per operazioni di riordinamento

---

## 🎯 Criteri di Accettazione Globali

1. ✅ Viaggi esistenti continuano a funzionare (stages array vuoto)
2. ✅ Nuovi viaggi possono essere creati con più tappe (stages array popolato)
3. ✅ Calcolo automatico distanza totale e durata basato su `isMultiStageTrip()`
4. ✅ Interface intuitiva per riordinamento tappe
5. ✅ Performance mantenute rispetto a situazione attuale
6. ✅ Test coverage > 80% per nuove funzionalità
7. ✅ Logica chiara senza campi ridondanti

---

## 📝 Note per Implementazione

- **Testing First**: implementare test prima dell'implementazione
- **Atomic Changes**: ogni task deve essere deployabile separatamente  
- **Documentation**: aggiornare documentazione API per ogni endpoint
- **Code Review**: review obbligatorio per modifiche al database schema
- **Backup Strategy**: backup completo prima di ogni migrazione dati