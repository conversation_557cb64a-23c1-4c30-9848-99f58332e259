// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  binaryTargets = ["native", "rhel-openssl-1.0.x", "rhel-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?
  role          UserRole  @default(Explorer)
  bio           String?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  accounts      Account[]
  sessions      Session[]
  trips         Trip[]

  @@map("users")
}

enum UserRole {
  Explorer  // Utenti che fruiscono dei viaggi
  Ranger    // Utenti che possono creare e fruire dei viaggi
  Sentinel  // Ranger con poteri amministrativi
}

model EmailVerificationToken {
  id        String   @id @default(cuid())
  email     String
  token     String   @unique
  expiresAt DateTime
  createdAt DateTime @default(now())

  @@map("email_verification_tokens")
}

model Account {
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt
  user              User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@id([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  sessionToken String   @unique
  userId       String
  expires      DateTime
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String
  expires    DateTime

  @@id([identifier, token])
  @@map("verificationtokens")
}

model Trip {
  id                 String            @id @default(cuid())
  title              String
  summary            String
  destination        String
  duration_days      Int
  duration_nights    Int
  tags               String[]
  theme              String
  characteristics    String[]
  recommended_seasons RecommendedSeason[]
  media              Json[]            @default([])
  gpxFile            Json?             // Aggiunto campo per il file GPX
  insights           String?           @db.Text
  slug               String            @unique
  status             TripStatus        @default(Bozza)
  created_at         DateTime          @default(now())
  updated_at         DateTime          @updatedAt
  user_id            String
  user               User              @relation(fields: [user_id], references: [id])
  stages             Stage[]           // Relazione con le tappe

  @@map("trips")
}

model Stage {
  id                String     @id @default(cuid())
  tripId            String
  orderIndex        Int        // Numero ordinale della tappa (1, 2, 3...)
  title             String
  description       String?    @db.Text
  routeType         String?    // Tipo di percorso (testo libero)
  media             Json[]     @default([])
  gpxFile           Json?      // Traccia GPX della singola tappa
  createdAt         DateTime   @default(now())
  updatedAt         DateTime   @updatedAt
  trip              Trip       @relation(fields: [tripId], references: [id], onDelete: Cascade)
  
  @@unique([tripId, orderIndex])
  @@map("stages")
}

enum RecommendedSeason {
  Primavera
  Estate
  Autunno
  Inverno
}

enum TripStatus {
  Bozza
  Pronto_per_revisione @map("Pronto per revisione")
  Pubblicato
  Archiviato
}
